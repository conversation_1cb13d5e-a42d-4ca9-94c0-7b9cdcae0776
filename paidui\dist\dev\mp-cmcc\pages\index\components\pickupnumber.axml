<view class="{{('card') + ' ' + 'data-v-1f73e369' + ' ' + s}}"><view a:if="{{a}}" class="title data-v-1f73e369"><view class="title-name data-v-1f73e369">{{b}}</view><view class="title-btn data-v-1f73e369" onTap="{{d}}">{{c}}</view><view class="{{('data-v-1f73e369') + ' ' + e}}" onTap="{{f}}"></view></view><view class="content data-v-1f73e369"><view class="number-box data-v-1f73e369"><view class="number-box-title data-v-1f73e369">{{g}}</view><view class="number-box-number data-v-1f73e369">{{h}}</view><view class="number-box-wait data-v-1f73e369"><view class="data-v-1f73e369">{{i}}  </view><view class="wait-number data-v-1f73e369">{{j}}</view></view><view a:if="{{k}}" class="btn-item data-v-1f73e369" catchTap="{{m}}">{{l}}</view></view><base-info class="data-v-1f73e369" onGetWaitCount="{{n}}" u-i="1f73e369-0" onVI="__l" u-p="{{o}}"/><view class="tips-title data-v-1f73e369">{{p}}</view><view class="tips-content data-v-1f73e369">{{q}}</view></view><tips class="data-v-1f73e369" ref="__r" u-r="tipsPopup" onCancelQueuing="{{r}}" u-i="1f73e369-1" onVI="__l"/><view class="card-footer data-v-1f73e369"></view></view>