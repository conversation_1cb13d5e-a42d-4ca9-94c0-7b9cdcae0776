<view class="{{('card') + ' ' + 'data-v-c737fb5c' + ' ' + s}}"><view a:if="{{a}}" class="title data-v-c737fb5c"><view class="title-name data-v-c737fb5c">{{b}}</view><view class="title-btn data-v-c737fb5c" onTap="{{d}}">{{c}}</view><view class="{{('data-v-c737fb5c') + ' ' + e}}" onTap="{{f}}"></view></view><view class="content data-v-c737fb5c"><view class="number-box data-v-c737fb5c"><view class="number-box-title data-v-c737fb5c">{{g}}</view><view class="number-box-number data-v-c737fb5c">{{h}}</view><view class="number-box-wait data-v-c737fb5c"><view class="data-v-c737fb5c">{{i}}  </view><view class="wait-number data-v-c737fb5c">{{j}}</view></view><view a:if="{{k}}" class="btn-item data-v-c737fb5c" catchTap="{{m}}">{{l}}</view></view><base-info class="data-v-c737fb5c" onGetWaitCount="{{n}}" u-i="c737fb5c-0" onVI="__l" u-p="{{o}}"/><view class="tips-title data-v-c737fb5c">{{p}}</view><view class="tips-content data-v-c737fb5c">{{q}}</view></view><tips class="data-v-c737fb5c" ref="__r" u-r="tipsPopup" onCancelQueuing="{{r}}" u-i="c737fb5c-1" onVI="__l"/><view class="card-footer data-v-c737fb5c"></view></view>