<template>
  <view class="card" :class="[careClass]">
    <view v-if="!hideTitle" class="title">
      <view class="title-name">
        {{ $t('appoint') }}
      </view>
      <view class="title-btn" @click="toAppRecord()">
        {{ $t('appointRecord') }}
      </view>
      <view class="" :class="[careClass=='care-con' ? 'icon-arrow-blue' :'icon-arrow-blue-little']" @click="toAppRecord()"></view>
    </view>
    <view class="content">
      <BaseInfo basetyle="appointment" :hall-detail="hallDetail" />
      <view v-if="!hideTitle" class="tips">
        <view class="tips-p">
          <view class="iconfont icon-tips"></view>
          {{ $t('wxAppointCon1Tip') }}
        </view>
        <view class="tips-p">
          {{ $t('wxAppointCon2Tip') }}
        </view>
      </view>
      <view class="btn-con">
        <view class="btn btn-cancle" @click="cancelAppointment()">
          {{ $t('cancelAppoint') }}
        </view>
        <view class="btn btn-confirm" @click="userTakeNumber()">
          {{ $t('signTake') }}
        </view>
      </view>
      <template v-if="hideTitle">
        <view class="tips-title">
          {{ $t('wxTip') }}
        </view>
        <view class="tips-content">
          {{ $t('wxAppointSec1Tip') }}
        </view>
        <view class="tips-content">
          {{ $t('wxAppointSec2Tip') }}
        </view>
      </template>
    </view>
    <view class="card-footer"></view>
  </view>
</template>
<script>
import BaseInfo from "./baseinfo.vue"
import paiduiApi from "@/api/paidui.js"
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
import { useUserStore } from '@/store/user'
import { ERRORINFO } from "@/const/index.js"
const user = useUserStore()
export default {
  components:{
    BaseInfo
  },
  props:{
    hideTitle:{
      type:Boolean,
      default:false
    },
    hallDetail:{
      type:Object,
      default:()=>({})
    }
  },
  data(){
    return {
    }
  },
  computed: {
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    }
  },
  methods:{
    cancelAppointment(){
      let data = {
        mobile:user.info.msisdn,//手机号
        appNo:this.hallDetail.appNo,//预约序列号
        stationCode:this.hallDetail.stationCode,//店铺编码
      }
      paiduiApi.cancelAppointment(data).then(res=>{
        if(res.bizCode === "0000"){
          this.$emit('cancleAppointment')
          this.$emit('chagneTab','cancleAppointment')
        }
      })
      // this.$emit('cancleAppointment')
    },
    /**立即取号 */
    userTakeNumber(){
      let data = {
        stationCode: this.hallDetail.stationCode, //  营业厅编码
        takeType: 3, // 1代表现场扫码取号；2代表在线实时取号，3代表签到取号
        mobile: user.info.msisdn, // 手机号
        serviceCode: 'H', // 业务类型编码
        carrierOperator: "002", // 异网标识 001中国电信，002中国移动，003中国联通
        takeChannel: 1, // 预约渠道 预约渠道：1为一级手厅,2为一级微厅（公众号）,3为二级手厅,4为二级微厅（公众号）,5为小程序渠道
      };
      if(this.verType=='en'){
        data.queueVersion='en' // 英文版本
        data.enStationName=this.hallDetail.stationName // 英文名称
        data.enStationAddress=this.hallDetail.stationAddress // 英文名称
      }
      console.log('立即取号参数',data,this.hallDetail)
      paiduiApi.userTakeNumber(data,this.hallDetail.provinceCode).then(res=>{
        console.log('签到取号结果',res)
        if(res.bizCode == "0000"){
          this.$emit('queueHandle','cancleAppointment')
          this.$emit('chagneTab','pickupnumberSec')
        }else{
          uni.showToast({
            title:this.getErrorMsg(res.bizDesc)
          })
        }
      })
    },
    getErrorMsg(msg){
      if(this.verType=='en'){
        return ERRORINFO[msg] || "The system is busy. Please try again later"
      }
      return msg
    },
    toAppRecord(){
      let url = "/pages/recordlist/index?basetyle=appointment"
      uni.navigateTo({url: url})
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/home/<USER>";
.tips{
  font-size: 24px;
  color: rgba(0,0,0,0.4);
  line-height: 32px;
  margin-bottom: 30px;
  &-p{
    display: flex;
    align-items: center;
    justify-content: center;
    .icon-tips{
      margin-right:10px;
      color:#FF922A;
      font-size: 22px;
    }
  }
}
.tips-title{
  margin-top: 30px;
}
.care-con{
  margin-bottom: 50px;
  .title{
    height:80px;
    .title-name{
      font-size: 36px;
      font-weight: bold;
    }
    .title-btn{
      font-size: 32px;
      font-weight: bold;
    }
  }
  .tips-p{
    font-size: 32px;
    font-weight: bold;
    line-height: 48px;
    text-align: center;
    .icon-tips{
      font-size: 28px!important;
    }
  }
  .btn-con .btn{
    width: 258px;
    height: 80px;
    font-size: 40px;
    line-height: 78px;
  }
}
.en-con{
  .btn-con{
    display: block;
    .btn{
      width: 559px;
      font-size:28px;
    }
    .btn-confirm{
      margin-top:28px;
    }
  }
  .tips{
    text-align: center;
    &-p{
      display: inline!important;
      .icon-tips{
        font-size: 28px!important;
        display: inline-block;
      }
    }
  }
}
</style>
