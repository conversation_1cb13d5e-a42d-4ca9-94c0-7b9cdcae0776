<template>
  <div>
    <div class="search-con" :class="{'big':isBig,'small':!isBig,'care-con':bigFont}">
      <div v-if="!isBig" class="toogle-city" @click="openChangeProv">
        <span>{{ geo.cityName }}</span>
        <span class="icon-arrow-icon"></span>
      </div>
      <div class="search-bar"  @click="gotoNearby()" >
        <input
          v-model="searchCriteria"
          :placeholder="placeholder || $t('langPlaceholder')"
          placeholder-style="color:rgba(0,0,0,0.6)"
          :disabled="isBig ? true : false"
          @focus="gdpTrack('clk')"
        />
        <span class="iconfont icon-search-icon"></span>
      </div>
    </div>
    <ProvinceCon ref="provinceCon" @toggleCity="changeCity" />
  </div>
</template>
<script>
import {debounce} from '@/utils/utils.js'
import ProvinceCon from '@/components/province.vue'
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
import { gdpDcsMultiTrack } from "@/utils/gio-track.js"

export default {
  data() {
    return {
      searchCriteria: '',
      myInputDom:null
    }
  },
  props:{
    isBig:{
      type:Boolean
    },
    placeholder:{
      type:String,
      default:()=>{
        return ""
      }
    },
    bigFont:{
      type:Boolean,
      default:false
    }
  },
  components:{
    ProvinceCon
  },
  computed: {
    ...mapState(useUserStore, ['geo']),
  },
  watch:{
    searchCriteria:{
      handler(val){
        if(val){
          this.onChange()
        }
      },
      immediate:true
    }
  },
  created(){
    this.gdpTrack('imp')
  },
  methods:{
    onChange:debounce(function(){
      // uni.hideKeyboard()
      this.getNearShop()
    }, 1000),
    /**查询附近店铺数据 */
    getNearShop(){
      this.$emit('getNearShop',this.searchCriteria)
    },
    blurInput(){

    },
    /**跳转附近店铺页面 */
    gotoNearby(){
      if(this.isBig){
        // 店铺页面-跳转
        this.gdpTrack('clk')
        let url = '/pages/shoplist/index'
        uni.navigateTo({ url: url })
      }
    },
    openChangeProv(){
      this.$refs.provinceCon.open()
    },
    changeCity(val){
      let user = useUserStore()
      user.setGeo(val)
      this.getNearShop()
    },
    gdpTrack(type,url){
      const name = '搜索框'
      gdpDcsMultiTrack(type,{
        WT_et : type,
        WT_area_type_1 : '楼层',
        WT_area_name : name,
        WT_envName: name,
        XY_env_type: 'button'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/search/search.scss";
.icon-arrow-icon{
  width:17px;
  height:17px;
  background: url("@/static/home/<USER>") center no-repeat;
  background-size: contain;
  margin: 0 20px 0 8px;
  transform: rotate(90deg);
}
.toogle-city{
  display: flex;
  align-items: center;
}
.care-con{
  .toogle-city{
    font-size: 36px;
  }
}
</style>
