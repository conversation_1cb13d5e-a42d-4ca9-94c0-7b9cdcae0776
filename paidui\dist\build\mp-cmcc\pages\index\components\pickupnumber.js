"use strict";const common_vendor=require("../../../common/vendor.js");const store_options=require("../../../store/options.js");const api_paidui=require("../../../api/paidui.js");const store_user=require("../../../store/user.js");const BaseInfo=()=>"./baseinfo.js";const Tips=()=>"../../../components/tips.js";const user=store_user.useUserStore();const _sfc_main={components:{BaseInfo:BaseInfo,Tips:Tips},props:{hideTitle:{type:Boolean,default:false},hallDetail:{type:Object,default:()=>({})}},data(){return{waitCountData:null}},computed:{...common_vendor.mapState(store_options.useOptionsStore,["verType"]),careClass:function(){if(this.verType=="care"){return"care-con"}if(this.verType=="en"){return"en-con"}return""},totalWaitCount:function(){if(this.hallDetail.queueWaitNum){return this.hallDetail.queueWaitNum}return 0}},methods:{toTickRecord(){let url="/pages/recordlist/index?basetyle=picknumber";common_vendor.index.navigateTo({url:url})},getWaitCount(res){this.waitCountData=res},showTips(){this.$refs.tipsPopup.showTips()},cancelQueuing(){let data={mobile:user.info.msisdn,stationCode:this.hallDetail.stationCode};api_paidui.paiduiApi.cancelTakeNumber(data).then((res=>{if(res.bizCode==="0000"){this.$refs.tipsPopup.closePopup();this.$emit("cancleAppointment");this.$emit("chagneTab","cancleAppointment")}}))}}};if(!Array){const _component_BaseInfo=common_vendor.resolveComponent("BaseInfo");const _component_Tips=common_vendor.resolveComponent("Tips");(_component_BaseInfo+_component_Tips)()}function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return common_vendor.e({a:!$props.hideTitle},!$props.hideTitle?{b:common_vendor.t(_ctx.$t("takeInfo")),c:common_vendor.t(_ctx.$t("takeRecord")),d:common_vendor.o(($event=>$options.toTickRecord())),e:common_vendor.n($options.careClass=="care-con"?"icon-arrow-blue":"icon-arrow-blue-little"),f:common_vendor.o(($event=>$options.toTickRecord()))}:{},{g:common_vendor.t(_ctx.$t("takeNumber")),h:common_vendor.t($props.hallDetail.ticketNo),i:common_vendor.t(_ctx.$t("waitCount")),j:common_vendor.t($options.totalWaitCount),k:$props.hallDetail.cancelTakeNumber===1},$props.hallDetail.cancelTakeNumber===1?{l:common_vendor.t(_ctx.$t("cancelQueue")),m:common_vendor.o(((...args)=>$options.showTips&&$options.showTips(...args)))}:{},{n:common_vendor.o($options.getWaitCount),o:common_vendor.p({basetyle:"picknumber",["hall-detail"]:$props.hallDetail}),p:common_vendor.t(_ctx.$t("wxTip")),q:common_vendor.t(_ctx.$t("wxTakeSecTip")),r:common_vendor.o($options.cancelQueuing),s:common_vendor.n($options.careClass)})}const Component=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-c737fb5c"]]);my.createComponent(Component);
