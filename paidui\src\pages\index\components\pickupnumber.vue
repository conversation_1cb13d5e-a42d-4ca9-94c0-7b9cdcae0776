<template>
  <view class="card" :class="[careClass]">
    <view v-if="!hideTitle" class="title">
      <view class="title-name">
        {{ $t('takeInfo') }}
      </view>
      <view class="title-btn" @click="toTickRecord()">
        {{ $t('takeRecord') }}
      </view>
      <view :class="[careClass=='care-con' ? 'icon-arrow-blue' :'icon-arrow-blue-little']" @click="toTickRecord()"></view>
    </view>
    <view class="content">
      <view class="number-box">
        <view class="number-box-title">
          {{ $t('takeNumber') }}
        </view>
        <view class="number-box-number">
          {{ hallDetail.ticketNo }}
        </view>
        <view class="number-box-wait">
          <view>
            {{ $t('waitCount') }}&nbsp;
          </view>
          <view class="wait-number">
            {{ totalWaitCount }}
          </view>
        </view>
        <view v-if="hallDetail.cancelTakeNumber===1" class="btn-item" @click.stop="showTips">
          {{ $t('cancelQueue') }}
        </view>
      </view>
      <BaseInfo basetyle="picknumber" :hall-detail="hallDetail" @getWaitCount="getWaitCount" />
      <view class="tips-title">
        {{ $t('wxTip') }}
      </view>
      <view class="tips-content">
        {{ $t('wxTakeSecTip') }}
      </view>
    </view>
    <Tips ref="tipsPopup" @cancelQueuing="cancelQueuing" />
    <view class="card-footer"></view>
  </view>
</template>
<script>
import BaseInfo from "./baseinfo.vue"
import { useOptionsStore } from '@/store/options.js'
import Tips from '@/components/tips.vue'
import { mapState } from 'pinia'
import paiduiApi from "@/api/paidui.js"
import { useUserStore } from '@/store/user'
const user = useUserStore()
export default {
  components:{
    BaseInfo,
    Tips
  },
  props:{
    hideTitle:{
      type:Boolean,
      default:false
    },
    hallDetail:{
      type:Object,
      default:()=>({})
    }
  },
  data(){
    return {
      waitCountData:null
    }
  },
  computed:{
    ...mapState(useOptionsStore,['verType']),
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    },
    totalWaitCount:function(){
      if(this.hallDetail.queueWaitNum){
        return this.hallDetail.queueWaitNum
      }
      return 0
    }
  },
  methods:{
    toTickRecord(){
      let url = "/pages/recordlist/index?basetyle=picknumber"
      uni.navigateTo({url: url});
    },
    getWaitCount(res){
      this.waitCountData = res
    },
    showTips(){
      this.$refs.tipsPopup.showTips()
    },
    cancelQueuing(){
      const data = {
        mobile:user.info.msisdn,//手机号
        stationCode:this.hallDetail.stationCode,//店铺编码
      }
      paiduiApi.cancelTakeNumber(data).then(res=>{
        if(res.bizCode === "0000"){
          this.$refs.tipsPopup.closePopup()
          this.$emit('cancleAppointment')
          this.$emit('chagneTab','cancleAppointment')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/home/<USER>";
.number-box{
  width:283px;
  min-height:274px;
  margin:34px auto 0;
  text-align: center;
  padding-top:12px;
  &-title,&-wait{
    font-size: 28px;
    color:#000;
    line-height: 40px;
  }
  &-number{
    font-size: 90px;
    color: #007EFF;
    line-height: 72px;
    font-weight: bold;
    margin:24px auto 24px;
  }
  &-wait{
    display: flex;
    align-items:center;
    justify-content: center;
    margin-top:12px;
    line-height: 62px;
    .wait-number{
      font-size: 44px;
      font-weight: bold;
      margin-left:10px;
    }
  }
}
.btn-item{
  font-size: 32px;
  width: 258px;
  height: 80px;
  box-sizing: border-box;
  border-radius: 90px 90px 90px 90px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #007EFF;
  color:#007EFF;
  margin:20px auto 0;
}
.care-con{
  margin-bottom: 50px;
  .title{
    height:80px;
    .title-name{
      font-size: 36px;
      font-weight: bold;
    }
    .title-btn{
      font-size: 32px;
      font-weight: bold;
    }
    .icon-arrow1{
      font-size: 32px;
    }
  }
  .number-box{
    padding-top:2px;
    height:auto!important;
  }
  .number-box-number{
    margin:36px auto 18px!important;
    font-size: 104px!important;
    line-height: 104px!important;
  }
  .number-box-title{
    font-size: 32px!important;
    font-weight: bold;
  }
  .number-box-wait{
    font-size: 36px!important;
    font-weight: bold;
    line-height: 44px!important;
  }
  .wait-number{
    font-size: 44px!important;
  }
  &.content{
    margin-top:48px!important;
  }
  .btn-item{
    font-size: 40px;
  }
}
.en-con{
  .title-name{
    width:300px;
    font-size: 24px!important;
  }
  .title-btn{
    width: 230px;
  }
  .btn-item{
    width: 290px;
  }
}
</style>
