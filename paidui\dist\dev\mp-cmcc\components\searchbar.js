"use strict";
const common_vendor = require("../common/vendor.js");
const utils_utils = require("../utils/utils.js");
const store_user = require("../store/user.js");
const utils_gioTrack = require("../utils/gio-track.js");
const ProvinceCon = () => "./province.js";
const _sfc_main = {
  data() {
    return {
      searchCriteria: "",
      myInputDom: null
    };
  },
  props: {
    isBig: {
      type: Boolean
    },
    placeholder: {
      type: String,
      default: () => {
        return "";
      }
    },
    bigFont: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ProvinceCon
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["geo"])
  },
  watch: {
    searchCriteria: {
      handler(val) {
        if (val) {
          this.onChange();
        }
      },
      immediate: true
    }
  },
  created() {
    this.gdpTrack("imp");
  },
  methods: {
    onChange: utils_utils.debounce(function() {
      this.getNearShop();
    }, 1e3),
    /**查询附近店铺数据 */
    getNearShop() {
      this.$emit("getNearShop", this.searchCriteria);
    },
    blurInput() {
    },
    /**跳转附近店铺页面 */
    gotoNearby() {
      if (this.isBig) {
        this.gdpTrack("clk");
        let url = "/pages/shoplist/index";
        common_vendor.index.navigateTo({ url });
      }
    },
    openChangeProv() {
      this.$refs.provinceCon.open();
    },
    changeCity(val) {
      let user = store_user.useUserStore();
      user.setGeo(val);
      this.getNearShop();
    },
    gdpTrack(type, url) {
      const name = "搜索框";
      utils_gioTrack.gdpDcsMultiTrack(type, {
        WT_et: type,
        WT_area_type_1: "楼层",
        WT_area_name: name,
        WT_envName: name,
        XY_env_type: "button"
      });
    }
  }
};
if (!Array) {
  const _component_ProvinceCon = common_vendor.resolveComponent("ProvinceCon");
  _component_ProvinceCon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$props.isBig
  }, !$props.isBig ? {
    b: common_vendor.t(_ctx.geo.cityName),
    c: common_vendor.o((...args) => $options.openChangeProv && $options.openChangeProv(...args))
  } : {}, {
    d: $props.placeholder || _ctx.$t("langPlaceholder"),
    e: $props.isBig ? true : false,
    f: common_vendor.o(($event) => $options.gdpTrack("clk")),
    g: $data.searchCriteria,
    h: common_vendor.o(($event) => $data.searchCriteria = $event.detail.value),
    i: common_vendor.o(($event) => $options.gotoNearby()),
    j: $props.isBig ? 1 : "",
    k: !$props.isBig ? 1 : "",
    l: $props.bigFont ? 1 : "",
    m: common_vendor.o($options.changeCity)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d926064c"]]);
my.createComponent(Component);
