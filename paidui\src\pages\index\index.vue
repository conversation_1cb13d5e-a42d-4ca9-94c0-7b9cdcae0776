<template>
  <view v-show="hallDetail && hallDetail.stationCode" class="home" :class="[careClass]">
    <Searchbar v-if="careClass!=='en-con'" :is-big="true" :bigFont="careClass=='care-con'" />
    <view class="container">
      <ShopInfo :hall-detail="hallDetail" :show-tag="showTag" :show-tips="showTips" @goStoreList="goStoreList" @gdpTrack="gdpTrack" />
      <view class="card-con">
        <BaseCard v-if="cardType=='basecard'" :hall-detail="hallDetail" :base-card-tyle="baseCardType" @chagneTab="changeCardType" @gdpTrack="gdpTrack" />
        <Pickupnumber v-if="cardType=='pickupnumber'" :hall-detail="queueInfo"  @chagneTab="changeCardType" />
        <Appointment v-if="cardType=='appointment'" :hall-detail="appRecord" @chagneTab="changeCardType" />
      </view>
    </view>
    <view v-if="showResult" class="result-con">
      <ResultCom :hall-detail="hallDetail" :card-type="cardType" @chagneTab="changeCardType" />
    </view>
  </view>
  <EMPTY v-if="isEmpty" @refresh="logined" />
</template>
<script>
import Searchbar from '@/components/searchbar.vue';
import EMPTY from '@/components/empty.vue';
import ShopInfo from "./components/shopinfo.vue";
import Appointment from './components/appointment.vue';
import Pickupnumber from './components/pickupnumber.vue';
import ResultCom from './components/resultcom.vue';
import BaseCard from './components/basecard.vue';
import paiduiApi from '@/api/paidui.js';
import { useUserStore } from '@/store/user';
import { useShopInfoStore } from '@/store/shopdetail.js';
import { useOptionsStore } from '@/store/options.js'
import { mapState } from 'pinia'
import { APPOINTMENTSTATUS, ENUM_VERTYPE } from '@/const/index.js'
import { getLocation } from '@/utils/location'
import {gdpDcsMultiTrack,gdpSetGeneralProps, gdpSetUserId} from "@/utils/gio-track.js"
import yundianApi from "@/api/yundian.js"
const shopInfo = useShopInfoStore()
const CONSTANTS = {
  BIZ_CODE: {
    SUCCESS: '0000',
    UNAUTHORIZED: '401'
  },
  CARD_TYPE: {
    BASE: 'basecard',
    PICKUP: 'pickupnumber',
    APPOINTMENT: 'appointment'
  }
}
export default {
  data() {
    return {
      cardType:"", // basecard,pickupnumber,appointment
      baseCardType:"baseCard", // baseCard,baseAppointCard
      stationCode:"",
      hallDetail:{},
      distance: null,
      appRecord:{},
      queueInfo:{},
      showResult:false,
      baseType:'',
      showTips:false,
      options:{},
      from: '',
      unifiedChannelId:null, //新的店铺编码
      isEmpty:false,
      shopEnInfo:{}
    }
  },
  components:{
    Searchbar,
    ShopInfo,
    Appointment,
    Pickupnumber,
    BaseCard,
    ResultCom,
    EMPTY
  },
  computed: {
    ...mapState(useUserStore, ['info', 'isLogined','geo']),
    ...mapState(useOptionsStore,['verType']),
    showTag:function(){
      if((!this.stationCode) && ( this.geo && this.geo.latitude)){
        return true
      }else{
        return false
      }
    },
    careClass:function(){
      if(this.verType=='care'){
        return 'care-con'
      }
      if(this.verType=='en'){
        return 'en-con'
      }
      return  ''
    }
  },
  onLoad(options) {
    // uni.showLoading()
    // 河南 郑州 巩义 国通二号验证营业厅请勿取号 2371371021100400005
    // 广东 深圳 福田 国通一号测试系统请勿取号 220075501000000000S
    this.stationCode = options.stationCode
    this.baseType = options.baseType   //appointment || ""
    this.from = options.from
    this.unifiedChannelId = options.unifiedChannelId
    let {provinceCode:province,cityCode:city} = options
    let userStore = useUserStore()
    userStore.setGeo({
      ...this.geo,
      province,
      city
    })
    const optionsStore = useOptionsStore()
    optionsStore.setVerType(options)
    if(this.isLogined){
      this.logined()
    }
    my.setNavigationBar({
      reset: true,
      title: this.$t('mpappTitle'),
    });
  },
  onShow: function() {
    if(this.hallDetail && this.hallDetail.provinceCode){
      let userStore = useUserStore()
      userStore.setGeo({
        ...this.geo,
        province:this.hallDetail.provinceCode,
        city:this.hallDetail.cityCode,
        cityName:this.hallDetail.cityName
      })
    }
    let isBack = uni.getStorageSync('isBack')
    if(isBack && this.isLogined){
      this.logined()
    }
  },
  watch:{
    isLogined: {
      handler(newVal) {
        if(newVal){
          this.logined()

        }
      }
    }
  },
  methods: {
    async logined(){
      uni.showLoading()
      // 指定店铺获取店铺详情
      if((this.stationCode && this.from === 'fjt')||this.unifiedChannelId) {
        let stationCode = this.unifiedChannelId || this.stationCode
        await this.queryChannelCode(stationCode)
      }
      // 获取取号预约信息
      // this.queryQueueAppInfo()
      this.queryHomeData()
      // this.multiTrack()
    },
    /**页面展示切换 */
    changeCardType(type,queueRes){
      switch(type){
        case "pickupnumberSec":
        case "appointmentSec":
          this.openResultDialog(type,queueRes)
          break;
        case "appointment":
          this.baseCardType="baseAppointCard"
          this.gdpTrack('imp','确定预约')
          break;
        case "cancleAppointment" :
          this.baseCardType="baseCard"
          this.cardType = 'basecard'
          this.showResult=false
          this.gdpTrack('imp','立即预约')
          this.gdpTrack('imp','在线预约')
      }
    },
    /**获取取号&预约结果 */
    async openResultDialog(type,queueRes){
      if(type=='appointmentSec'){
        let result = await this.queryUserAppRecords()
        let resultEn = {}
        if(this.verType==='en' && result && result.stationCode){
          resultEn = await this.getShopEnInfo(result.stationCode)
        }
        if(result){
          this.hallDetail = {
            ...this.hallDetail,
            number:this.info.msisdn,
            ...result,
            distance: result.distance || this.distance,
            ...resultEn
          }
          this.cardType = 'appointment'
          this.showResult = true
          console.log("预约成功模拟",result)
        }
      }
      if(type=='pickupnumberSec'){
        this.queueInfo = queueRes.resultData
        let resultEn = {}
        if(this.verType==='en' && this.queueInfo && this.queueInfo.stationCode){
          resultEn = await this.getShopEnInfo(this.queueInfo.stationCode)
        }
        this.hallDetail = {
          ...this.hallDetail,
          ...this.queueInfo,
          ...resultEn
        }
        if(this.queueInfo){
          this.cardType = 'pickupnumber'
          this.showResult = true
          console.log("取号成功模拟",queueRes.resultData)
        }
      }
      setTimeout(()=>{
        //弹框展示5秒后关闭
        this.logined()
        this.showResult = false
      },5000)
    },
    /**
     * 去云店查询stationCode
     */
    queryChannelCode (stationCode) {
      return new Promise((resolve) => {
        yundianApi.queryChannelCode(stationCode).then(res=> {
          if(res && res.data && res.data.length == 19) {
            this.stationCode = res.data
          }else{
            this.stationCode = stationCode
          }
          resolve(stationCode)
        }).catch(()=> {
          resolve(stationCode)
        })
      })
    },
    queryShopInfo({unifiedChannelIdList,stationCodeList}){
      return new Promise((resolve) => {
        yundianApi.queryShopInfo({unifiedChannelIdList,stationCodeList}).then(res=> {
          console.log("queryShopInfo===",{unifiedChannelIdList,stationCodeList},res)
          if(res && res.data && res.data.shopEnInfoList && res.data.shopEnInfoList.length>0 && this.verType==='en') {
            resolve(res.data.shopEnInfoList[0])
          }
          if(res && res.data && res.data.shopInfoList && res.data.shopInfoList.length>0) {
            resolve(res.data.shopInfoList[0])
          }
          resolve({})
        }).catch(()=> {
          resolve({})
        })
      })
    },
    /**获取取号&预约&附近店铺信息 */
    async queryHomeData(){
      await getLocation(this.geo.province,this.geo.city)
      const params = {
        "latitude": this.geo.latitude || '0.0',
        "longitude": this.geo.longitude || '0.0',
        "mobile": this.info.msisdn,
        "stationCode":this.stationCode
      }
      let res = await paiduiApi.queryHomeData(params)
      this.multiTrack()
      if (res.bizCode === CONSTANTS.BIZ_CODE.SUCCESS) {
        this.isEmpty = false
        console.log("getHomeData====1========res.resultData.franchisee=" + res.resultData.franchisee);
        if (res.resultData.franchisee === 2) {
          const params = {
            mobile: this.info.msisdn,
            stationCode: res.resultData.nearStationDetail.stationCode,
          }
          let resQ = await paiduiApi.queryHomeDataWithQueueNumber(params);
          console.log(resQ)
          if (resQ.bizCode === CONSTANTS.BIZ_CODE.SUCCESS) {
            let setNum = res.resultData.nearStationDetail.setNum;
            let totalWaitCount = res.resultData.nearStationDetail.totalWaitCount;
            resQ.resultData.nearStationDetail = res.resultData.nearStationDetail;//将第一次请求获取的厅店信息补充到detail
            resQ.resultData.nearStationDetail.setNum = setNum;//更新台席数
            resQ.resultData.nearStationDetail.totalWaitCount = totalWaitCount;//更新等待人数
            this.handleNumber(resQ)
          }
        } else {
          this.handleNumber(res);
        }
      }else{
        console.log(res,88888888888889,'未登录')
        this.getHomeError(res)
      }
    },
    async getHomeError(res){
      if(res.bizCode === CONSTANTS.BIZ_CODE.UNAUTHORIZED || res.bizCode==='A0100') {
        //未登录
        return false
      }
      let enInfo = await this.getShopEnInfo()
      if(enInfo.stationCode){
        this.isEmpty = false
        this.handleNumber({
          resultData: {
            nearStationDetail:enInfo
          }
        },false)
      }else{
        uni.hideLoading()
        this.isEmpty = true
      }
    },
    async getShopEnInfo(stationCode){
      if(this.shopEnInfo[stationCode]){
        return this.shopEnInfo[stationCode]
      }
      let enData = {}
      if(stationCode){
        enData = await this.queryShopInfo({
          stationCodeList:[stationCode]
        })
      }else if(this.unifiedChannelId){
        enData = await this.queryShopInfo({
          unifiedChannelIdList:[this.unifiedChannelId]
        })
      }else if(this.stationCode){
        enData = await this.queryShopInfo({
          stationCodeList:[this.stationCode]
        })
      }
      this.shopEnInfo[stationCode] = enData
      return enData
    },
    /**取号&预约&附近店铺信息处理 */
    async handleNumber(res,hasPaiduiData=true) {
      uni.hideLoading()
      uni.stopPullDownRefresh();
      this.showTipsFn()
      if (res.resultData.appRecord) {//有预约
        let enInfo = {}
        if(this.verType==='en'){
          enInfo = await this.getShopEnInfo(res.resultData.nearStationDetail.stationCode)
        }
        this.appRecord = {
          ...res.resultData.appRecord,
          number:this.info.msisdn,
          stationAddress:res.resultData.appStationDetail.stationAddress,
          provinceCode:res.resultData.appStationDetail.provinceCode,
          ...enInfo
        }
        this.appRecord.appHandleDate = this.appRecord.appDate+' '+this.appRecord.startTime+ '~' +this.appRecord.endTime
        this.cardType="appointment"
      } else if (res.resultData.takeNumberRecord) {//有取号
        let enInfo = {}
        if(this.verType==='en'){
          enInfo = await this.getShopEnInfo(res.resultData.nearStationDetail.stationCode)
        }
        this.queueInfo = {...res.resultData.takeNumberRecord,...enInfo}
        this.cardType="pickupnumber"
      }else{
        this.cardType = 'basecard'
        //指定店铺在线预约状态
        if(this.baseType == "appointment"){
          this.baseCardType="baseAppointCard"
          return false
        }
      }
      //有取号，或者有预约，或者没有跳转到其他页面，都要填充home
      if (res.resultData.nearStationDetail) {//处理附近厅，取号信息或预约信息tab
        console.log("------------------------init home data--------------------")
        let enInfo = {}
        if(this.verType==='en' && hasPaiduiData){
          enInfo = await this.getShopEnInfo(res.resultData.nearStationDetail.stationCode)
        }
        this.hallDetail = {
          ...res.resultData.nearStationDetail,
          ...enInfo
        }
        this.hallDetail.serviceInfoShort = this.hallDetail.newServiceInfo ? this.hallDetail.newServiceInfo.slice(0,4) : []
      } else {//没有取号没有预约，没有最近厅店，跳转至厅店列表页？
        this.$dialog.alert({
          title: this.$t('notice'),
          message: '该省暂未查询到厅店信息，请切换至其他省份'
        }).then(() => {
          this.$router.push({
            name: "hallList",
          });
        });
      }
    },
    /** 获取页面详情 */
    getHallDetail() {
      paiduiApi.queryHallDetail({
        "latitude": this.geo.latitude,
        "longitude": this.geo.longitude,
        "stationCode": this.stationCode,
        "mobile": this.info.msisdn,
      }).then(res=> {
        if(res.bizCode == CONSTANTS.BIZ_CODE.SUCCESS){
          this.hallDetail = {
            ...this.hallDetail,
            ...res.resultData,
            distance: res.resultData.distance || this.distance
          }
          this.hallDetail.serviceInfoShort = res.resultData.newServiceInfo.slice(0,4)
          shopInfo.setShopInfo(this.hallDetail)
          console.log("营业厅详情",this.hallDetail)
        }
      })
    },
    /**获取用户预约信息 */
    queryUserAppRecords(){
      return new Promise((resolve)=>{
        paiduiApi.queryUserAppRecords({
          "currentPage":1,"pageSize":1,
          "mobile": this.info.msisdn,
        }).then(appInfoRes=> {
          console.log("查询用户应用记录",appInfoRes)
          if(appInfoRes.bizCode == CONSTANTS.BIZ_CODE.SUCCESS && appInfoRes.resultData.appList && appInfoRes.resultData.appList.list[0] && appInfoRes.resultData.appList.list[0]){
            let appInfo = appInfoRes.resultData.appList.list[0]
            if(appInfo.appointmentStatus==APPOINTMENTSTATUS.SUCCESS.CODE){
              appInfo.appHandleDate = appInfo.appDate+' '+appInfo.startTime+ '~' +appInfo.endTime
              resolve(appInfo)
            }else{
              resolve(false)
            }
          }else{
            resolve(false)
          }
        })
      })
    },
    /**跳转列表页 */
    goStoreList(){
      let url =  `/pages/shoplist/index?provinceCode=${this.geo.province}&cityCode=${this.geo.city}`
      uni.navigateTo({url: url})
    },
    /**温馨提示展示时间 */
    showTipsFn(){
      this.showTips = true
      setTimeout(()=>{
        this.showTips = false
      },10000)
    },
    /**插码 */
    async multiTrack(){
      await gdpSetGeneralProps({
        WT_page_type: '排队取号_' + ENUM_VERTYPE[this.verType],
      })

      gdpSetUserId(this.info.gdpUserId)

      gdpDcsMultiTrack('pageView',{
        "WT_et" : "pageview",
        "WT_event" : "MPXPageShow",
        "WT_ti" : "预约取号"
      })
      this.gdpTrack('imp','切换营业厅')
      this.gdpTrack('imp','地图导航')
      if(this.baseCardType=='baseAppointCard'){
        //确认预约页面
        this.gdpTrack('imp','确定预约')
      }
      if(this.baseCardType=='baseCard'){
        //立即取号页面
        this.gdpTrack('imp','立即取号')
        this.gdpTrack('imp','在线预约')
        this.gdpTrack('imp','历史记录')
      }
    },
    gdpTrack(type,name){
      gdpDcsMultiTrack(type,{
        WT_et : type,
        WT_area_type_1 : '楼层',
        WT_area_name : name,
        WT_envName: name,
        XY_env_type: 'button',
        WT_page_type:ENUM_VERTYPE[this.verType] + '_排队取号_' + name
      })
    }
  },
  onPullDownRefresh(){
    this.queryHomeData()
    // setTimeout(() => {
    //   uni.stopPullDownRefresh();
    // }, 1000);
  }
}
</script>
<style lang="scss" scoped>
.home{
  background-color: #fff;
}
.container{
  min-height:100vh;
  background: url('@/static/home/<USER>') no-repeat;
  background-size: cover;
  padding:30px;
  box-sizing: border-box;
}
.card-con{
  margin-top:27px;
}
.result-con{
  position:fixed;
  top:0;
  bottom: 0;
  left:0;
  right:0;
}
.care-con .result-con,.en-con .result-con{
  overflow: auto;
}
</style>
