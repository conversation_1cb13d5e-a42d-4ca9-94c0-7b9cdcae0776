"use strict";const api_paidui=require("../../api/paidui.js");const store_user=require("../../store/user.js");const common_vendor=require("../../common/vendor.js");const utils_location=require("../../utils/location.js");const store_options=require("../../store/options.js");const utils_region=require("../../utils/region.js");const SearchBar=()=>"../../components/searchbar.js";const ShopItem=()=>"./components/shopitem.js";const ShopItemCare=()=>"./components/shopitem-care.js";const _sfc_main={data(){return{hallList:[],currentPage:1,pageSize:10,finished:false,searchCriteria:"",status:"more",showEmpty:false,maxLength:4,careClass:null}},components:{SearchBar:SearchBar,ShopItem:ShopItem,ShopItemCare:ShopItemCare},computed:{...common_vendor.mapState(store_user.useUserStore,["info","isLogined","geo"]),...common_vendor.mapState(store_options.useOptionsStore,["verType"])},onLoad(options){if(this.verType=="care"){this.careClass="care-con";this.maxLength=3}const optionsStore=store_options.useOptionsStore();optionsStore.setVerType(options);my.setNavigationBar({reset:true,title:this.$t("mpappTitle")})},watch:{isLogined:{handler(newVal){if(newVal){this.logined()}},immediate:true}},methods:{async logined(){await utils_location.getLocationAndProv({provinceCode:this.geo.province,cityCode:this.geo.city});utils_region.initRegion();this.getHallList()},searchShop(searchCriteria){this.searchCriteria=searchCriteria;this.currentPage=1;this.finished=false;this.hallList=[];this.getHallList()},getHallList(){let data={mobile:this.info.msisdn||"",latitude:this.geo.latitude,longitude:this.geo.longitude,currentPage:this.currentPage,pageSize:this.pageSize,cityCode:this.geo.city};if(this.searchCriteria)data.searchCriteria=this.searchCriteria;this.status="loading";this.showEmpty=false;api_paidui.paiduiApi.queryHallList(data).then((res=>{this.$refs.searchbarref.blurInput();if(res.resultData){if(this.currentPage===1){this.hallList=[]}const processedList=res.resultData.list.map((item=>({...item,serviceInfoShort:(item.newServiceInfo||"").slice(0,this.maxLength)})));this.hallList=this.hallList.concat(processedList);if(res.resultData.total>res.resultData.pageSize*res.resultData.pageNo){this.currentPage++;this.status="more"}else{this.finished=true;this.status="no-more";if(this.hallList.length==0){this.showEmpty=true}}}})).catch((error=>{console.error("获取营业厅列表失败:",error);this.finished=true;this.status="no-more";if(this.hallList.length==0){this.showEmpty=true}}))}},onReachBottom(){if(!this.finished){this.getHallList()}}};if(!Array){const _component_SearchBar=common_vendor.resolveComponent("SearchBar");const _component_ShopItemCare=common_vendor.resolveComponent("ShopItemCare");const _component_ShopItem=common_vendor.resolveComponent("ShopItem");const _easycom_uni_load_more2=common_vendor.resolveComponent("uni-load-more");(_component_SearchBar+_component_ShopItemCare+_component_ShopItem+_easycom_uni_load_more2)()}const _easycom_uni_load_more=()=>"../../node-modules/npm-scope-dcloudio/uni-ui/lib/uni-load-more/uni-load-more.js";if(!Math){_easycom_uni_load_more()}function _sfc_render(_ctx,_cache,$props,$setup,$data,$options){return common_vendor.e({a:_ctx.isLogined},_ctx.isLogined?common_vendor.e({b:common_vendor.o($options.searchShop),c:common_vendor.p({["is-big"]:false,placeholder:"请输入营业厅名称/地址",bigFont:$data.careClass=="care-con"}),d:$data.careClass=="care-con"},$data.careClass=="care-con"?{e:common_vendor.f($data.hallList,((item,k0,i0)=>({a:item.stationCode,b:"e24fdbac-1-"+i0,c:common_vendor.p({item:item,["max-num-length"]:$data.maxLength})})))}:{f:common_vendor.f($data.hallList,((item,k0,i0)=>({a:item.stationCode,b:"e24fdbac-2-"+i0,c:common_vendor.p({item:item,["max-num-length"]:$data.maxLength})})))},{g:!$data.showEmpty},!$data.showEmpty?{h:common_vendor.p({iconType:"auto",status:$data.status})}:{},{i:$data.showEmpty},$data.showEmpty?{}:{},{j:common_vendor.n($data.careClass)}):{})}const MiniProgramPage=common_vendor._export_sfc(_sfc_main,[["render",_sfc_render]]);my.createPage(MiniProgramPage);
